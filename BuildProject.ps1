# PowerShell脚本用于构建PetShop项目
# 请确保Visual Studio或MSBuild已安装

Write-Host "正在构建PetShop项目..." -ForegroundColor Green

# 查找MSBuild
$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe"
)

$msbuild = $null
foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuild = $path
        break
    }
}

if (-not $msbuild) {
    Write-Host "错误: 未找到MSBuild。请确保Visual Studio已正确安装。" -ForegroundColor Red
    Write-Host "或者尝试在Visual Studio Developer Command Prompt中运行此脚本。" -ForegroundColor Yellow
    exit 1
}

Write-Host "找到MSBuild: $msbuild" -ForegroundColor Green

# 还原NuGet包
Write-Host "正在还原NuGet包..." -ForegroundColor Yellow
try {
    $nuget = Get-Command nuget -ErrorAction SilentlyContinue
    if ($nuget) {
        nuget restore PetShop.sln
    } else {
        Write-Host "警告: 未找到NuGet命令行工具。请在Visual Studio中手动还原包。" -ForegroundColor Yellow
    }
} catch {
    Write-Host "警告: NuGet包还原可能失败。请在Visual Studio中手动还原包。" -ForegroundColor Yellow
}

# 构建项目
Write-Host "正在构建项目..." -ForegroundColor Yellow
try {
    & $msbuild "PetShop.sln" /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "项目构建成功！" -ForegroundColor Green
        Write-Host "现在可以在Visual Studio中运行项目了。" -ForegroundColor Cyan
    } else {
        Write-Host "项目构建失败，请检查错误信息。" -ForegroundColor Red
    }
} catch {
    Write-Host "构建项目时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "按任意键继续..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
