# 宠物商店网站 - 快速启动指南

## 🚀 快速启动步骤

### 1. 创建数据库
在项目根目录运行PowerShell脚本：
```powershell
.\CreateDatabase.ps1
```

或者手动执行：
1. 打开SQL Server Management Studio
2. 连接到 `.\SQLEXPRESS`
3. 执行 `Database_Scripts\Shop_sql2.sql` 脚本

### 2. 在Visual Studio中打开项目
1. 双击 `PetShop.sln` 文件
2. 等待Visual Studio加载项目
3. 如果提示还原NuGet包，点击"还原"

### 3. 运行项目
1. 确保 `PetShop` 项目设为启动项目
2. 按 `F5` 或点击"开始调试"
3. 浏览器将自动打开网站

## 🔧 如果遇到编译错误

### 常见解决方案：
1. **右键点击解决方案** → **重新生成解决方案**
2. **工具** → **NuGet包管理器** → **程序包管理器控制台**，运行：
   ```
   Update-Package -reinstall
   ```
3. **项目** → **管理NuGet程序包** → **浏览** → 搜索并安装：
   - EntityFramework (6.2.0)
   - Microsoft.CodeDom.Providers.DotNetCompilerPlatform (2.0.1)

## 🎯 测试账户

### 管理员账户
- 用户名：`admin`
- 密码：`123`
- 功能：可以访问后台管理

### 普通用户账户  
- 用户名：`Jack`
- 密码：`123`
- 功能：普通用户权限

## 📋 功能测试清单

### ✅ 基础功能测试
- [ ] 访问首页 (`index.aspx`)
- [ ] 用户注册 (`userAdd.aspx`)
- [ ] 用户登录 (`login.aspx`)
- [ ] 查看个人信息 (`myinfo.aspx`)
- [ ] 浏览所有商品 (`allproduct.aspx`)
- [ ] 搜索商品功能
- [ ] 管理员登录后台 (`index_m.aspx`)

### 🔍 测试流程
1. **新用户注册**：
   - 访问注册页面
   - 填写用户信息
   - 验证注册成功

2. **用户登录**：
   - 使用注册的账户登录
   - 验证跳转到个人信息页面

3. **管理员登录**：
   - 使用 admin/123 登录
   - 验证显示"登入到后台"按钮
   - 点击进入后台管理

4. **商品浏览**：
   - 查看所有商品页面
   - 测试搜索功能
   - 点击商品查看详情

## 🛠️ 技术说明

### 项目结构
- **前台模板**：`q.Master`
- **后台模板**：`h.Master`  
- **数据库**：MyPetShop (SQL Server)
- **ORM**：Entity Framework 6.2.0

### 数据库连接
项目使用以下连接字符串：
```
data source=.\SQLEXPRESS;initial catalog=MyPetShop;integrated security=True
```

如果你的SQL Server实例名不同，请修改 `Web.config` 中的连接字符串。

## ❗ 故障排除

### 数据库连接问题
- 确保SQL Server服务正在运行
- 检查SQL Server实例名是否为 `SQLEXPRESS`
- 验证数据库 `MyPetShop` 已创建

### 编译错误
- 确保安装了 .NET Framework 4.7.2
- 检查所有NuGet包是否正确安装
- 尝试清理并重新生成解决方案

### 页面显示问题
- 确保IIS Express正在运行
- 检查图片文件是否存在于 `Images` 和 `Prod_Images` 目录
- 验证所有 `.aspx` 页面文件完整

## 📞 支持

如果遇到问题，请检查：
1. Visual Studio版本（推荐2017或更高版本）
2. .NET Framework 4.7.2是否已安装
3. SQL Server是否正确配置
4. 项目文件是否完整

项目已完全复制参考网页的功能和设计，应该可以正常运行！
