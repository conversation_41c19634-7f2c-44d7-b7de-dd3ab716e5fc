# 宠物商店网站 (PetShop)

这是一个基于ASP.NET Web Forms的宠物商店网站，完全复制了参考项目的功能和设计。

## 技术栈

- **框架**: ASP.NET Web Forms (.NET Framework 4.7.2)
- **数据库**: SQL Server (MyPetShop)
- **ORM**: Entity Framework 6.2.0
- **开发工具**: Visual Studio
- **服务器**: IIS Express

## 项目结构

```
PetShop/
├── PetShop.sln                    # 解决方案文件
├── PetShop/
│   ├── PetShop.csproj             # 项目文件
│   ├── Web.config                 # 配置文件
│   ├── packages.config            # NuGet包配置
│   ├── Model1.Context.cs          # Entity Framework DbContext
│   ├── q.Master                   # 前台主页面模板
│   ├── h.Master                   # 后台主页面模板
│   ├── Images/                    # 图片资源
│   ├── Prod_Images/               # 产品图片
│   └── *.aspx                     # 页面文件
├── Database_Scripts/
│   └── Shop_sql2.sql              # 数据库脚本
└── CreateDatabase.ps1             # 数据库创建脚本
```

## 安装和运行

### 1. 环境要求

- Visual Studio 2017 或更高版本
- .NET Framework 4.7.2
- SQL Server 2016 或更高版本 (或 SQL Server Express)
- IIS Express (通常随Visual Studio安装)

### 2. 数据库设置

#### 方法一：使用PowerShell脚本（推荐）
```powershell
# 在项目根目录运行
.\CreateDatabase.ps1
```

#### 方法二：手动执行SQL脚本
1. 打开SQL Server Management Studio
2. 连接到SQL Server实例 (.\SQLEXPRESS)
3. 打开并执行 `Database_Scripts\Shop_sql2.sql`

### 3. 项目配置

1. 打开 `PetShop.sln` 解决方案文件
2. 检查 `Web.config` 中的数据库连接字符串：
   ```xml
   <connectionStrings>
     <add name="MyPetShopEntities" 
          connectionString="metadata=res://*/Model1.csdl|res://*/Model1.ssdl|res://*/Model1.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\SQLEXPRESS;initial catalog=MyPetShop;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" 
          providerName="System.Data.EntityClient" />
   </connectionStrings>
   ```
3. 如果需要，修改数据源为你的SQL Server实例名

### 4. 运行项目

1. 在Visual Studio中设置 `PetShop` 为启动项目
2. 按 F5 或点击"开始调试"
3. 浏览器将自动打开并显示网站首页

## 功能特性

### 前台功能
- **首页**: 欢迎页面
- **用户注册**: 新用户注册功能
- **用户登录**: 用户身份验证
- **个人信息**: 查看和管理个人信息
- **商品浏览**: 查看所有商品
- **商品搜索**: 按名称搜索商品
- **商品详情**: 查看商品详细信息

### 后台功能（管理员）
- **后台管理**: 管理员专用界面
- **用户管理**: 用户列表和添加
- **商品管理**: 商品列表、添加和分类管理

### 用户角色
- **普通用户**: 可以浏览商品、注册、登录
- **管理员**: 拥有后台管理权限

## 默认账户

### 管理员账户
- 用户名: `admin`
- 密码: `123`
- 邮箱: `<EMAIL>`

### 测试用户
- 用户名: `Jack`
- 密码: `123`
- 邮箱: `<EMAIL>`

## 数据库结构

### 主要数据表
1. **Category** - 商品分类表
2. **Customer** - 客户表
3. **Product** - 产品表
4. **Order** - 订单表
5. **OrderItem** - 订单项表
6. **CartItem** - 购物车表
7. **Supplier** - 供应商表

### 初始数据
- 5个商品分类：Fish, Bugs, Backyard, Birds, Endangered
- 10个示例产品
- 2个供应商
- 2个测试用户

## 页面导航

### 前台页面
- `index.aspx` - 首页
- `login.aspx` - 登录页面
- `userAdd.aspx` - 用户注册页面
- `myinfo.aspx` - 个人信息页面
- `allproduct.aspx` - 所有商品页面
- `productSearch.aspx` - 商品搜索页面
- `productDetail.aspx` - 商品详情页面

### 后台页面
- `index_m.aspx` - 后台管理首页

## 技术实现细节

### Session管理
- `Session["UserId"]` - 用户ID
- `Session["UserName"]` - 用户名
- `Session["UserEmail"]` - 用户邮箱

### URL参数
- `productDetail.aspx?pid={ProductId}` - 产品详情
- `productSearch.aspx?pn={ProductName}` - 产品搜索

### 样式设计
- 使用内联CSS样式
- 表格布局设计
- 响应式图片显示
- 统一的颜色主题

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查SQL Server服务是否运行
   - 验证连接字符串中的服务器名称
   - 确保数据库已正确创建

2. **页面显示错误**
   - 检查IIS Express是否正常运行
   - 验证所有必需的NuGet包已安装
   - 确保.NET Framework 4.7.2已安装

3. **图片不显示**
   - 确认Images和Prod_Images文件夹中的图片文件存在
   - 检查图片路径是否正确

## 开发说明

这个项目完全复制了参考网页的功能、布局、技术实现和参数命名规范，包括：

- 相同的页面结构和样式
- 一致的数据库设计
- 相同的Entity Framework模型
- 完全一致的控件命名规范
- 相同的Session和URL参数传递方式
- 一致的用户流程和功能逻辑

## 许可证

本项目仅用于学习和演示目的。
