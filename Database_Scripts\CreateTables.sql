-- 在MyPetShop数据库中创建表和插入数据
-- 请先手动创建MyPetShop数据库，然后执行此脚本

USE MyPetShop
GO

-- 创建Category表
CREATE TABLE [Category] (
    [CategoryId] int identity PRIMARY KEY,
    [Name] nvarchar(80) NULL,
    [Descn] nvarchar(255) NULL
)

-- 创建Customer表
CREATE TABLE [Customer] (
    [CustomerId] int identity PRIMARY KEY,
    [Name] [nvarchar](80) NOT NULL,
    [Password] [nvarchar](80) NOT NULL,
    [Email] [nvarchar](80) NOT NULL
)

-- 创建Supplier表
CREATE TABLE [Supplier] (
    [SuppId] int identity PRIMARY KEY,
    [Name] nvarchar(80) NULL,
    [Addr1] nvarchar(80) NULL,
    [Addr2] nvarchar(80) NULL,
    [City] nvarchar(80) NULL,
    [State] nvarchar(80) NULL,
    [Zip] nvarchar(6) NULL,
    [Phone] nvarchar(40) NULL
)

-- 创建Product表
CREATE TABLE [Product] (
    [ProductId] int identity PRIMARY KEY,
    [CategoryId] int NOT NULL REFERENCES [Category]([CategoryId]),
    [ListPrice] decimal(10, 2) NULL,
    [UnitCost] decimal(10, 2) NULL,
    [SuppId] int NULL REFERENCES [Supplier]([SuppId]),
    [Name] nvarchar(80) NULL,
    [Descn] nvarchar(255) NULL,
    [Image] nvarchar(80) NULL,
    [Qty] int NOT NULL
)

-- 创建Order表
CREATE TABLE [Order] (
    [OrderId] int identity PRIMARY KEY,
    [CustomerId] int NOT NULL REFERENCES [Customer]([CustomerId]),
    [UserName] nvarchar(80) NOT NULL,
    [OrderDate] datetime NOT NULL,
    [Addr1] nvarchar(80) NULL,
    [Addr2] nvarchar(80) NULL,
    [City] nvarchar(80) NULL,
    [State] nvarchar(80) NULL,
    [Zip] nvarchar(6) NULL,
    [Phone] nvarchar(40) NULL,
    [Status] nvarchar(10) NULL
)

-- 创建OrderItem表
CREATE TABLE [OrderItem] (
    [ItemId] int identity PRIMARY KEY,
    [OrderId] int NOT NULL REFERENCES [Order]([OrderId]),
    [ProName] nvarchar(80),
    [ListPrice] decimal(10, 2) NULL,
    [Qty] int NOT NULL,
    [TotalPrice] decimal(10, 2) NULL
)

-- 创建CartItem表
CREATE TABLE [CartItem] (
    [CartItemId] int identity PRIMARY KEY,
    [CustomerId] int NOT NULL REFERENCES [Customer]([CustomerId]),
    [ProId] int NOT NULL REFERENCES [Product]([ProductId]),
    [ProName] [nvarchar](80) NOT NULL,
    [ListPrice] [decimal](10, 2) NOT NULL,
    [Qty] [int] NOT NULL
)

-- 插入Category数据
INSERT INTO [Category] VALUES ('Fish', 'Fish')
INSERT INTO [Category] VALUES ('Bugs', 'Bugs')
INSERT INTO [Category] VALUES ('Backyard', 'Backyard')
INSERT INTO [Category] VALUES ('Birds', 'Birds')
INSERT INTO [Category] VALUES ('Endangered', 'Endangered')

-- 插入Customer数据
INSERT [Customer] ([Name], [Password], [Email]) VALUES ('admin', '123', '<EMAIL>')
INSERT [Customer] ([Name], [Password], [Email]) VALUES ('Jack', '123', '<EMAIL>')

-- 插入Supplier数据
INSERT INTO [Supplier] VALUES ('XYZ Pets', '600 Avon Way', '', 'Los Angeles', 'CA', '94024', '212-947-0797')
INSERT INTO [Supplier] VALUES ('ABC Pets', '700 Abalone Way', '', 'San Francisco', 'CA', '94024', '415-947-0797')

-- 插入Product数据
INSERT INTO [Product] VALUES (1, 12.1, 11.4, 1, 'Meno', 'Meno', '~/Prod_Images/meno.gif', 100)
INSERT INTO [Product] VALUES (1, 28.5, 25.5, 1, 'Eucalyptus', 'Eucalyptus', '~/Prod_Images/eucalyptus.gif', 100)
INSERT INTO [Product] VALUES (2, 23.4, 11.4, 1, 'Ant', 'Ant', '~/Prod_Images/ant.gif', 100)
INSERT INTO [Product] VALUES (2, 24.7, 22.2, 1, 'Butterfly', 'Butterfly', '~/Prod_Images/butterfly.gif', 100)
INSERT INTO [Product] VALUES (3, 38.5, 37.2, 1, 'Cat', 'Cat', '~/Prod_Images/cat.gif', 100)
INSERT INTO [Product] VALUES (3, 40.4, 38.7, 1, 'Zebra', 'Zebra', '~/Prod_Images/zebra.gif', 100)
INSERT INTO [Product] VALUES (4, 45.5, 44.2, 1, 'Domestic', 'Domestic', '~/Prod_Images/domestic.gif', 100)
INSERT INTO [Product] VALUES (4, 25.2, 23.5, 1, 'Flowerloving', 'Flowerloving', '~/Prod_Images/flowerloving.gif', 100)
INSERT INTO [Product] VALUES (5, 47.7, 45.5, 1, 'Panda', 'Panda', '~/Prod_Images/panda.gif', 100)
INSERT INTO [Product] VALUES (5, 35.5, 33.5, 1, 'Pointy', 'Pointy', '~/Prod_Images/pointy.gif', 100)

-- 创建存储过程
CREATE PROCEDURE CategoryInsert
    (
    @Name varchar(80),
    @Descn varchar(255)
    )
AS
    INSERT INTO Category(Name,Descn) VALUES (@Name,@Descn);
    RETURN
GO

-- 验证数据插入
SELECT 'Tables Created' as Status, COUNT(*) as TableCount 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'

SELECT 'Categories' as DataType, COUNT(*) as Count FROM Category
UNION ALL
SELECT 'Customers' as DataType, COUNT(*) as Count FROM Customer
UNION ALL
SELECT 'Products' as DataType, COUNT(*) as Count FROM Product
UNION ALL
SELECT 'Suppliers' as DataType, COUNT(*) as Count FROM Supplier

PRINT 'Database setup completed successfully!'
PRINT 'Test accounts:'
PRINT 'Admin: admin / 123'
PRINT 'User: Jack / 123'
