<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="myinfo.aspx.cs" Inherits="PetShop.myinfo" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>个人信息</h2>

    <table border="1" cellpadding="5" cellspacing="0">
        <tr>
            <td>用户ID:</td>
            <td><asp:Label ID="lbl_userid" runat="server" Text=""></asp:Label></td>
        </tr>
        <tr>
            <td>用户名:</td>
            <td><asp:Label ID="lbl_username" runat="server" Text=""></asp:Label></td>
        </tr>
        <tr>
            <td>邮箱:</td>
            <td><asp:Label ID="lbl_email" runat="server" Text=""></asp:Label></td>
        </tr>
    </table>

    <br /><br />

    <asp:Button ID="btn_admin" runat="server" OnClick="btn_admin_Click" Text="登入到后台" Visible="false" />
    <asp:Button ID="btn_logout" runat="server" OnClick="btn_logout_Click" Text="退出登录" />
    <asp:Button ID="btn_back" runat="server" OnClick="btn_back_Click" Text="返回首页" />

    <br /><br />

    <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Red"></asp:Label>
</asp:Content>
