@echo off
echo 正在创建MyPetShop数据库...
echo.

REM 检查sqlcmd是否可用
where sqlcmd >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到sqlcmd命令。请确保SQL Server已正确安装。
    pause
    exit /b 1
)

echo 找到SQL Server命令行工具
echo 正在执行数据库脚本...
echo.

REM 执行数据库脚本
sqlcmd -S ".\SQLEXPRESS" -i "Database_Scripts\Shop_sql2.sql"

if %errorlevel% equ 0 (
    echo.
    echo 数据库创建成功！
    echo 数据库名称: MyPetShop
    echo 默认用户: admin ^(密码: 123^)
    echo 测试用户: Jack ^(密码: 123^)
) else (
    echo.
    echo 数据库创建失败，请检查错误信息。
)

echo.
pause
