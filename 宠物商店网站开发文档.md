# 宠物商店网站开发文档

## 项目概述

### 技术栈
- **框架**: ASP.NET Web Forms (.NET Framework 4.7.2)
- **数据库**: SQL Server (MyPetShop)
- **ORM**: Entity Framework 6.2.0
- **开发工具**: Visual Studio
- **服务器**: IIS Express

### 项目结构
```
Web1/
├── Web1.sln                    # 解决方案文件
├── Web1/
│   ├── Web1.csproj            # 项目文件
│   ├── Web.config             # 配置文件
│   ├── packages.config        # NuGet包配置
│   ├── Model1.edmx            # Entity Framework模型
│   ├── q.Master               # 前台主页面模板
│   ├── h.Master               # 后台主页面模板
│   ├── Images/                # 图片资源
│   ├── Prod_Images/           # 产品图片
│   └── *.aspx                 # 页面文件
└── Database_Scripts/
    └── Shop_sql2.sql          # 数据库脚本
```

## 数据库设计

### 数据库名称
`MyPetShop`

### 数据表结构

#### 1. Category (商品分类表)
```sql
CREATE TABLE [Category] (
    [CategoryId] int identity PRIMARY KEY,
    [Name] nvarchar(80) NULL,
    [Descn] nvarchar(255) NULL
)
```

#### 2. Customer (客户表)
```sql
CREATE TABLE [Customer] (
    [CustomerId] int identity PRIMARY KEY,
    [Name] [nvarchar](80) NOT NULL,
    [Password] [nvarchar](80) NOT NULL,
    [Email] [nvarchar](80) NOT NULL
)
```

#### 3. Product (产品表)
```sql
CREATE TABLE [Product] (
    [ProductId] int identity PRIMARY KEY,
    [CategoryId] int NOT NULL REFERENCES [Category]([CategoryId]),
    [ListPrice] decimal(10, 2) NULL,
    [UnitCost] decimal(10, 2) NULL,
    [SuppId] int NULL REFERENCES [Supplier]([SuppId]),
    [Name] nvarchar(80) NULL,
    [Descn] nvarchar(255) NULL,
    [Image] nvarchar(80) NULL,
    [Qty] int NOT NULL
)
```

#### 4. Order (订单表)
```sql
CREATE TABLE [Order] (
    [OrderId] int identity PRIMARY KEY,
    [CustomerId] int NOT NULL REFERENCES [Customer]([CustomerId]),
    [UserName] nvarchar(80) NOT NULL,
    [OrderDate] datetime NOT NULL,
    [Addr1] nvarchar(80) NULL,
    [Addr2] nvarchar(80) NULL,
    [City] nvarchar(80) NULL,
    [State] nvarchar(80) NULL,
    [Zip] nvarchar(6) NULL,
    [Phone] nvarchar(40) NULL,
    [Status] nvarchar(10) NULL
)
```

#### 5. OrderItem (订单项表)
```sql
CREATE TABLE [OrderItem] (
    [ItemId] int identity PRIMARY KEY,
    [OrderId] int NOT NULL REFERENCES [Order]([OrderId]),
    [ProName] nvarchar(80),
    [ListPrice] decimal(10, 2) NULL,
    [Qty] int NOT NULL,
    [TotalPrice] decimal(10, 2) NULL
)
```

#### 6. CartItem (购物车表)
```sql
CREATE TABLE [CartItem] (
    [CartItemId] int identity PRIMARY KEY,
    [CustomerId] int NOT NULL REFERENCES [Customer]([CustomerId]),
    [ProId] int NOT NULL REFERENCES [Product]([ProductId]),
    [ProName] [nvarchar](80) NOT NULL,
    [ListPrice] [decimal](10, 2) NOT NULL,
    [Qty] [int] NOT NULL
)
```

#### 7. Supplier (供应商表)
```sql
CREATE TABLE [Supplier] (
    [SuppId] int identity PRIMARY KEY,
    [Name] nvarchar(80) NULL,
    [Addr1] nvarchar(80) NULL,
    [Addr2] nvarchar(80) NULL,
    [City] nvarchar(80) NULL,
    [State] nvarchar(80) NULL,
    [Zip] nvarchar(6) NULL,
    [Phone] nvarchar(40) NULL
)
```

### 初始数据
```sql
-- 分类数据
INSERT INTO [Category] VALUES ('Fish', 'Fish')
INSERT INTO [Category] VALUES ('Bugs', 'Bugs')
INSERT INTO [Category] VALUES ('Backyard', 'Backyard')
INSERT INTO [Category] VALUES ('Birds', 'Birds')
INSERT INTO [Category] VALUES ('Endangered', 'Endangered')

-- 用户数据
INSERT [Customer] ([Name], [Password], [Email]) VALUES ('admin', '123', '<EMAIL>')
INSERT [Customer] ([Name], [Password], [Email]) VALUES ('Jack', '123', '<EMAIL>')

-- 供应商数据
INSERT INTO [Supplier] VALUES ('XYZ Pets', '600 Avon Way', '', 'Los Angeles', 'CA', '94024', '************')
INSERT INTO [Supplier] VALUES ('ABC Pets', '700 Abalone Way', '', 'San Francisco', 'CA', '94024', '************')

-- 产品数据
INSERT INTO [Product] VALUES (1, 12.1, 11.4, 1, 'Meno', 'Meno', '~/Prod_Images/meno.gif', 100)
INSERT INTO [Product] VALUES (1, 28.5, 25.5, 1, 'Eucalyptus', 'Eucalyptus', '~/Prod_Images/eucalyptus.gif', 100)
INSERT INTO [Product] VALUES (2, 23.4, 11.4, 1, 'Ant', 'Ant', '~/Prod_Images/ant.gif', 100)
INSERT INTO [Product] VALUES (2, 24.7, 22.2, 1, 'Butterfly', 'Butterfly', '~/Prod_Images/butterfly.gif', 100)
INSERT INTO [Product] VALUES (3, 38.5, 37.2, 1, 'Cat', 'Cat', '~/Prod_Images/cat.gif', 100)
INSERT INTO [Product] VALUES (3, 40.4, 38.7, 1, 'Zebra', 'Zebra', '~/Prod_Images/zebra.gif', 100)
INSERT INTO [Product] VALUES (4, 45.5, 44.2, 1, 'Domestic', 'Domestic', '~/Prod_Images/domestic.gif', 100)
INSERT INTO [Product] VALUES (4, 25.2, 23.5, 1, 'Flowerloving', 'Flowerloving', '~/Prod_Images/flowerloving.gif', 100)
INSERT INTO [Product] VALUES (5, 47.7, 45.5, 1, 'Panda', 'Panda', '~/Prod_Images/panda.gif', 100)
INSERT INTO [Product] VALUES (5, 35.5, 33.5, 1, 'Pointy', 'Pointy', '~/Prod_Images/pointy.gif', 100)
```

## 配置文件

### Web.config
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <compilation debug="true" targetFramework="4.7.2" />
    <httpRuntime targetFramework="4.7.2" />
  </system.web>

  <connectionStrings>
    <add name="MyPetShopEntities"
         connectionString="metadata=res://*/Model1.csdl|res://*/Model1.ssdl|res://*/Model1.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=.\SQLEXPRESS;initial catalog=MyPetShop;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;"
         providerName="System.Data.EntityClient" />
  </connectionStrings>

  <appSettings>
    <add key="ValidationSettings:UnobtrusiveValidationMode" value="None"/>
  </appSettings>

  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs"
                type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                warningLevel="4"
                compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb"
                type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
                warningLevel="4"
                compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
</configuration>
```

### packages.config
```xml
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net472" />
  <package id="EntityFramework.zh-Hans" version="6.2.0" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net472" />
</packages>
```

## Entity Framework 模型

### DbContext 类
```csharp
namespace Web1
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;

    public partial class MyPetShopEntities : DbContext
    {
        public MyPetShopEntities()
            : base("name=MyPetShopEntities")
        {
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }

        public virtual DbSet<CartItem> CartItem { get; set; }
        public virtual DbSet<Category> Category { get; set; }
        public virtual DbSet<Customer> Customer { get; set; }
        public virtual DbSet<Order> Order { get; set; }
        public virtual DbSet<OrderItem> OrderItem { get; set; }
        public virtual DbSet<Product> Product { get; set; }
        public virtual DbSet<Supplier> Supplier { get; set; }
    }
}
```

## 主页面模板设计

### 前台主页面模板 (q.Master)

#### 样式定义
```css
.auto-style1 { width: 800px; }
.auto-style2 { width: 100%; }
.auto-style3 { width: 78px; }
.auto-style5 { background-color: #00FFCC; }
.auto-style6 { color: #FFFFFF; background-color: #99CC00; }
.auto-style7 { background-color: aliceblue; }
.auto-left { background-color: cornsilk; width: 200px; height: 400px; }
.auto-right { width: 600px; height: 400px; }
.auto-style8 { width: 100%; background-color: #CCFFFF; }
.auto-style9 { width: 60px; height: 20px; }
.auto-style11 { height: 20px; }
.auto-style12 { width: 65px; height: 31px; }
```

#### 页面结构
```html
<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="q.master.cs" Inherits="Web519.q" %>
<!DOCTYPE html>
<html>
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
    <!-- 样式定义 -->
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <table cellpadding="0" cellspacing="0" class="auto-style1">
                <!-- 头部导航区域 -->
                <tr>
                    <td colspan="2">
                        <table cellpadding="0" cellspacing="0" class="auto-style2">
                            <tr>
                                <td class="auto-style3">
                                    <img alt="" class="auto-style12" src="Images/logo.gif" />
                                </td>
                                <td>
                                    <table cellpadding="0" cellspacing="0" class="auto-style2">
                                        <tr>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl="~/index.aspx">首页</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink7" runat="server" NavigateUrl="~/allproduct.aspx">所有商品</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink2" runat="server" NavigateUrl="~/userAdd.aspx">注册</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink3" runat="server" NavigateUrl="~/login.aspx">登录</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink4" runat="server" NavigateUrl="~/index.aspx">购物车</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink5" runat="server" NavigateUrl="~/myinfo.aspx">人个信息</asp:HyperLink>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="5">
                                                <asp:Label ID="Label1" runat="server" CssClass="auto-style6" Text="您还没有登录"></asp:Label>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!-- 位置导航 -->
                <tr>
                    <td colspan="2">您的位置：</td>
                </tr>
                <!-- 主体内容区域 -->
                <tr>
                    <td class="auto-left" valign="top">
                        商品分类<br /><br />
                    </td>
                    <td class="auto-right" valign="top">
                        <!-- 搜索区域 -->
                        <table cellpadding="0" cellspacing="0" class="auto-style8">
                            <tr>
                                <td class="auto-style11">
                                    <img alt="" class="auto-style9" src="Images/google_logo.gif" />
                                    <asp:TextBox ID="TextBox1" runat="server"></asp:TextBox>
                                    <asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="~/Images/searchbutton.gif" OnClick="ImageButton1_Click" />
                                </td>
                            </tr>
                        </table>
                        <br />
                        <!-- 内容占位符 -->
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </td>
                </tr>
                <!-- 页脚 -->
                <tr>
                    <td class="auto-style7">&nbsp;</td>
                    <td class="auto-style7">Copyright 2025</td>
                </tr>
            </table>
        </div>
    </form>
</body>
</html>
```

#### 后台代码 (q.Master.cs)
```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web519
{
    public partial class q : System.Web.UI.MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }

        protected void ImageButton1_Click(object sender, ImageClickEventArgs e)
        {
            string condi = this.TextBox1.Text;
            Response.Redirect("productSearch.aspx?pn=" + condi);
        }
    }
}
```

### 后台主页面模板 (h.Master)

#### 样式定义
```css
.auto-style1 { width: 800px; }
.auto-style2 { width: 100%; }
.auto-style3 { width: 78px; }
.auto-style5 { background-color: #00FFCC; height: 16px; }
.auto-style7 { background-color: aliceblue; }
.auto-left { background-color: cornsilk; width: 200px; height: 400px; }
.auto-right { width: 600px; height: 400px; }
.auto-style12 { width: 65px; height: 31px; }
.auto-style13 { font-size: xx-large; }
```

#### 页面结构
```html
<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="h.master.cs" Inherits="Web1.h" %>
<!DOCTYPE html>
<html>
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
    <!-- 样式定义 -->
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <table cellpadding="0" cellspacing="0" class="auto-style1">
                <!-- 头部区域 -->
                <tr>
                    <td colspan="2">
                        <table cellpadding="0" cellspacing="0" class="auto-style2">
                            <tr>
                                <td class="auto-style3">
                                    <img alt="" class="auto-style12" src="Images/logo.gif" />
                                </td>
                                <td>
                                    <table cellpadding="0" cellspacing="0" class="auto-style2">
                                        <tr>
                                            <td class="auto-style5"></td>
                                            <td class="auto-style5">&nbsp;</td>
                                            <td class="auto-style5">&nbsp;</td>
                                            <td class="auto-style5">&nbsp;</td>
                                            <td class="auto-style5">&nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td colspan="5" class="auto-style13">后台管理系统</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!-- 状态栏 -->
                <tr>
                    <td colspan="2">
                        <asp:Label ID="Label1" runat="server" Text="Label"></asp:Label>
                    </td>
                </tr>
                <!-- 主体内容区域 -->
                <tr>
                    <td class="auto-left" valign="top">
                        <br /><br />
                        <!-- 导航树 -->
                        <asp:TreeView ID="TreeView1" runat="server">
                            <Nodes>
                                <asp:TreeNode NavigateUrl="~/index_m.aspx" Text="manage" Value="manage">
                                    <asp:TreeNode Text="用户信息管理" Value="新建节点">
                                        <asp:TreeNode NavigateUrl="custList.aspx" Text="用户列表" Value="用户列表"></asp:TreeNode>
                                        <asp:TreeNode NavigateUrl="custAdd.aspx" Text="用户添加" Value="用户添加"></asp:TreeNode>
                                    </asp:TreeNode>
                                    <asp:TreeNode Text="商品信息管理" Value="商品信息管理">
                                        <asp:TreeNode NavigateUrl="productList.aspx" Text="商品列表" Value="商品列表"></asp:TreeNode>
                                        <asp:TreeNode NavigateUrl="productAdd.aspx" Text="商品添加" Value="商品添加"></asp:TreeNode>
                                        <asp:TreeNode NavigateUrl="productCate.aspx" Text="商品分类" Value="商品分类"></asp:TreeNode>
                                    </asp:TreeNode>
                                </asp:TreeNode>
                            </Nodes>
                        </asp:TreeView>
                    </td>
                    <td class="auto-right" valign="top">
                        <!-- 内容占位符 -->
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </td>
                </tr>
                <!-- 页脚 -->
                <tr>
                    <td class="auto-style7">&nbsp;</td>
                    <td class="auto-style7">Copyright 2025</td>
                </tr>
            </table>
        </div>
    </form>
</body>
</html>
```

#### 后台代码 (h.Master.cs)
```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class h : System.Web.UI.MasterPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }
    }
}
```

## 核心页面实现

### 1. 首页 (index.aspx)
```html
<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="index.aspx.cs" Inherits="Web1.index" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    Welcome!!!
</asp:Content>
```

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class index : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }
    }
}
```

### 2. 用户登录页面 (login.aspx)
```html
<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="login.aspx.cs" Inherits="Web1.login" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>用户登录</h2>

    用户名:<asp:TextBox ID="txt_id" runat="server"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_id" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    密码:<asp:TextBox ID="txt_pwd" runat="server" TextMode="Password"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txt_pwd" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="登录" />
    <asp:Button ID="Button2" runat="server" OnClick="Button2_Click" Text="注册" CausesValidation="false" />
    <br /><br />

    <asp:Label ID="Label1" runat="server" Text="" ForeColor="Red"></asp:Label>
</asp:Content>
```

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            string username = this.txt_id.Text.Trim();
            string pwd = this.txt_pwd.Text.Trim();

            try
            {
                MyPetShopEntities db = new MyPetShopEntities();

                // 根据用户名查找用户
                Customer c = db.Customer.FirstOrDefault(u => u.Name == username);

                if (c != null)
                {
                    if (c.Password == pwd)
                    {
                        // 登录成功，保存用户信息到Session
                        Session["UserId"] = c.CustomerId;
                        Session["UserName"] = c.Name;
                        Session["UserEmail"] = c.Email;

                        // 检查是否是管理员
                        if (username.ToLower() == "admin")
                        {
                            // 管理员登录，跳转到后台管理
                            Response.Redirect("index_m.aspx");
                        }
                        else
                        {
                            // 普通用户登录，跳转到个人信息页面
                            Response.Redirect("myinfo.aspx");
                        }
                    }
                    else
                    {
                        this.Label1.Text = "密码错误！";
                    }
                }
                else
                {
                    this.Label1.Text = "用户名不存在！";
                }
            }
            catch (Exception ex)
            {
                this.Label1.Text = "登录失败：" + ex.Message;
            }
        }

        // 添加注册按钮点击事件
        protected void Button2_Click(object sender, EventArgs e)
        {
            Response.Redirect("userAdd.aspx");
        }
    }
}
```

### 3. 用户注册页面 (userAdd.aspx)
```html
<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="userAdd.aspx.cs" Inherits="Web1.userAdd" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>用户注册</h2>

    用户名:<asp:TextBox ID="txt_username" runat="server"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_username" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    密码:<asp:TextBox ID="txt_password" runat="server" TextMode="Password"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txt_password" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    确认密码:<asp:TextBox ID="txt_confirm_password" runat="server" TextMode="Password"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txt_confirm_password" ErrorMessage="*"></asp:RequiredFieldValidator>
    <asp:CompareValidator ID="CompareValidator1" runat="server" ControlToValidate="txt_confirm_password" ControlToCompare="txt_password" ErrorMessage="密码不一致" ForeColor="Red"></asp:CompareValidator>
    <br /><br />

    邮箱:<asp:TextBox ID="txt_email" runat="server"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="txt_email" ErrorMessage="*"></asp:RequiredFieldValidator>
    <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txt_email" ErrorMessage="邮箱格式不正确" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ForeColor="Red"></asp:RegularExpressionValidator>
    <br /><br />

    <asp:Button ID="btn_register" runat="server" OnClick="btn_register_Click" Text="注册" />
    <asp:Button ID="btn_back" runat="server" OnClick="btn_back_Click" Text="返回登录" CausesValidation="false" />
    <br /><br />

    <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Red"></asp:Label>
</asp:Content>
```

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class userAdd : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lbl_message.Text = "";
            }
        }

        protected void btn_register_Click(object sender, EventArgs e)
        {
            string username = txt_username.Text.Trim();
            string password = txt_password.Text.Trim();
            string email = txt_email.Text.Trim();

            try
            {
                MyPetShopEntities db = new MyPetShopEntities();

                // 检查用户名是否已存在
                Customer existingUser = db.Customer.FirstOrDefault(c => c.Name == username);
                if (existingUser != null)
                {
                    lbl_message.Text = "用户名已存在，请选择其他用户名！";
                    return;
                }

                // 检查邮箱是否已存在
                Customer existingEmail = db.Customer.FirstOrDefault(c => c.Email == email);
                if (existingEmail != null)
                {
                    lbl_message.Text = "邮箱已被注册，请使用其他邮箱！";
                    return;
                }

                // 创建新用户
                Customer newUser = new Customer
                {
                    Name = username,
                    Password = password,
                    Email = email
                };

                db.Customer.Add(newUser);
                db.SaveChanges();

                lbl_message.ForeColor = System.Drawing.Color.Green;
                lbl_message.Text = "注册成功！3秒后跳转到登录页面...";

                // 3秒后跳转到登录页面
                Response.AddHeader("Refresh", "3;URL=login.aspx");
            }
            catch (Exception ex)
            {
                lbl_message.Text = "注册失败：" + ex.Message;
            }
        }

        protected void btn_back_Click(object sender, EventArgs e)
        {
            Response.Redirect("login.aspx");
        }
    }
}
```

### 4. 个人信息页面 (myinfo.aspx)
```html
<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="myinfo.aspx.cs" Inherits="Web1.myinfo" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <h2>个人信息</h2>

    <table border="1" cellpadding="5" cellspacing="0">
        <tr>
            <td>用户ID:</td>
            <td><asp:Label ID="lbl_userid" runat="server" Text=""></asp:Label></td>
        </tr>
        <tr>
            <td>用户名:</td>
            <td><asp:Label ID="lbl_username" runat="server" Text=""></asp:Label></td>
        </tr>
        <tr>
            <td>邮箱:</td>
            <td><asp:Label ID="lbl_email" runat="server" Text=""></asp:Label></td>
        </tr>
    </table>

    <br /><br />

    <asp:Button ID="btn_admin" runat="server" OnClick="btn_admin_Click" Text="登入到后台" Visible="false" />
    <asp:Button ID="btn_logout" runat="server" OnClick="btn_logout_Click" Text="退出登录" />
    <asp:Button ID="btn_back" runat="server" OnClick="btn_back_Click" Text="返回首页" />

    <br /><br />

    <asp:Label ID="lbl_message" runat="server" Text="" ForeColor="Red"></asp:Label>
</asp:Content>
```

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class myinfo : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // 检查用户是否已登录
                if (Session["UserId"] == null)
                {
                    Response.Redirect("login.aspx");
                    return;
                }

                // 显示用户信息
                LoadUserInfo();
            }
        }

        private void LoadUserInfo()
        {
            try
            {
                int userId = Convert.ToInt32(Session["UserId"]);
                string userName = Session["UserName"].ToString();
                string userEmail = Session["UserEmail"].ToString();

                lbl_userid.Text = userId.ToString();
                lbl_username.Text = userName;
                lbl_email.Text = userEmail;

                // 如果是管理员，显示"登入到后台"按钮
                if (userName.ToLower() == "admin")
                {
                    btn_admin.Visible = true;
                }
            }
            catch (Exception ex)
            {
                lbl_message.Text = "加载用户信息失败：" + ex.Message;
            }
        }

        protected void btn_admin_Click(object sender, EventArgs e)
        {
            // 跳转到后台管理页面
            Response.Redirect("index_m.aspx");
        }

        protected void btn_logout_Click(object sender, EventArgs e)
        {
            // 清除Session，退出登录
            Session.Clear();
            Session.Abandon();
            Response.Redirect("login.aspx");
        }

        protected void btn_back_Click(object sender, EventArgs e)
        {
            // 返回首页
            Response.Redirect("index.aspx");
        }
    }
}
```

### 5. 后台管理首页 (index_m.aspx)
```html
<%@ Page Title="" Language="C#" MasterPageFile="~/h.Master" AutoEventWireup="true" CodeBehind="index_m.aspx.cs" Inherits="Web1.index_m" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    welcome!!!
</asp:Content>
```

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class index_m : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }
    }
}
```

### 6. 所有商品页面 (allproduct.aspx)
```html
<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="allproduct.aspx.cs" Inherits="Web1.allproduct" %>
<asp:Content ID="Content1" runat="server" contentplaceholderid="ContentPlaceHolder1">
    <p>
        <br />
        <asp:DataList ID="DataList1" runat="server" DataSourceID="LinqDataSource1" RepeatColumns="4">
            <ItemTemplate>
                <asp:Image ID="Image1" runat="server" Height="61px" ImageUrl='<%# Eval("Image") %>' Width="77px" />
                <br />
                <asp:HyperLink ID="HyperLink8" runat="server" NavigateUrl='<%# "productDetail.aspx?pid="+Eval("ProductId") %>' Text='<%# Eval("Name") %>'></asp:HyperLink>
                <br />
                <asp:Label ID="Label2" runat="server" Text='<%# Eval("ListPrice") %>'></asp:Label>
            </ItemTemplate>
        </asp:DataList>
        <asp:LinqDataSource ID="LinqDataSource1" runat="server" ContextTypeName="Web1.MyPetShopEntities" EntityTypeName="" TableName="Product">
        </asp:LinqDataSource>
    </p>
</asp:Content>
```

## 关键参数传递规范

### Session 参数
- `Session["UserId"]` - 用户ID (int)
- `Session["UserName"]` - 用户名 (string)
- `Session["UserEmail"]` - 用户邮箱 (string)

### URL 参数
- `productDetail.aspx?pid={ProductId}` - 产品详情页面参数
- `productSearch.aspx?pn={ProductName}` - 产品搜索参数

### 控件命名规范
- 文本框：`txt_` 前缀 (如：txt_username, txt_password)
- 按钮：`btn_` 前缀 (如：btn_register, btn_login)
- 标签：`lbl_` 前缀 (如：lbl_message, lbl_username)
- 验证器：`RequiredFieldValidator1`, `CompareValidator1` 等

### 数据库连接字符串
```
data source=.\SQLEXPRESS;initial catalog=MyPetShop;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework
```

## 图片资源

### Images 目录
- `logo.gif` - 网站Logo
- `google_logo.gif` - 搜索图标
- `searchbutton.gif` - 搜索按钮
- `arrow.gif`, `close.gif`, `maxsize.gif`, `minsize.gif`, `new_flash.gif`, `spacer.gif`, `xml.gif` - 界面装饰图片

### Prod_Images 目录
- `meno.gif`, `eucalyptus.gif` - Fish类产品图片
- `ant.gif`, `butterfly.gif` - Bugs类产品图片
- `cat.gif`, `zebra.gif` - Backyard类产品图片
- `domestic.gif`, `flowerloving.gif` - Birds类产品图片
- `panda.gif`, `pointy.gif` - Endangered类产品图片
- `11.png`, `Saola.png` - 其他产品图片

## 项目创建步骤

### 1. 创建ASP.NET Web应用程序
- 选择 .NET Framework 4.7.2
- 选择 Web Forms 模板
- 启用 IIS Express

### 2. 安装NuGet包
```
Install-Package EntityFramework -Version 6.2.0
Install-Package EntityFramework.zh-Hans -Version 6.2.0
Install-Package Microsoft.CodeDom.Providers.DotNetCompilerPlatform -Version 2.0.1
```

### 3. 创建数据库
- 运行 `Shop_sql2.sql` 脚本创建数据库和表结构
- 插入初始数据

### 4. 添加Entity Framework模型
- 添加 ADO.NET Entity Data Model
- 选择 "从数据库生成"
- 连接到 MyPetShop 数据库
- 选择所有表

### 5. 创建主页面模板
- 添加 `q.Master` (前台模板)
- 添加 `h.Master` (后台模板)
- 设置样式和布局

### 6. 创建页面文件
- 按照文档中的代码创建各个 .aspx 页面
- 实现对应的 .aspx.cs 后台代码

### 7. 添加图片资源
- 创建 Images 和 Prod_Images 目录
- 添加所需的图片文件

### 8. 配置Web.config
- 设置数据库连接字符串
- 配置编译器选项
- 设置验证模式

## 测试账户
- 管理员：用户名 `admin`，密码 `123`
- 普通用户：用户名 `Jack`，密码 `123`

## 部署说明
- 确保目标服务器安装了 .NET Framework 4.7.2
- 确保目标服务器安装了 SQL Server
- 部署前先创建数据库并运行初始化脚本
- 修改 Web.config 中的数据库连接字符串以匹配目标环境
