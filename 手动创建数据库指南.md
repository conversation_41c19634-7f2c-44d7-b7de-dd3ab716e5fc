# 手动创建数据库指南

## 🎯 三种创建数据库的方法

### 方法一：使用修复后的PowerShell脚本
在VS的程序包管理器控制台中运行：
```powershell
.\CreateDatabase.ps1
```

### 方法二：使用批处理文件
双击运行 `CreateDatabase.bat` 文件

### 方法三：使用SQL Server Management Studio (推荐)

#### 步骤：
1. **打开SQL Server Management Studio (SSMS)**

2. **连接到SQL Server**
   - 服务器名称：`.\SQLEXPRESS` 或 `localhost\SQLEXPRESS`
   - 身份验证：Windows身份验证
   - 点击"连接"

3. **执行数据库脚本**
   - 点击"文件" → "打开" → "文件"
   - 浏览到项目目录，选择 `Database_Scripts\Shop_sql2.sql`
   - 点击"执行"按钮 (或按F5)

4. **验证创建成功**
   - 在对象资源管理器中刷新"数据库"节点
   - 应该看到新创建的 `MyPetShop` 数据库

## 🔍 验证数据库是否创建成功

在SSMS中执行以下查询：

```sql
-- 检查数据库是否存在
SELECT name FROM sys.databases WHERE name = 'MyPetShop';

-- 切换到MyPetShop数据库
USE MyPetShop;

-- 检查表是否创建
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';

-- 检查测试数据
SELECT COUNT(*) as UserCount FROM Customer;
SELECT COUNT(*) as ProductCount FROM Product;
SELECT COUNT(*) as CategoryCount FROM Category;
```

**预期结果：**
- 应该显示7个表：CartItem, Category, Customer, Order, OrderItem, Product, Supplier
- UserCount: 2 (admin 和 Jack)
- ProductCount: 10 (10个示例产品)
- CategoryCount: 5 (5个商品分类)

## 🛠️ 如果遇到连接问题

### 检查SQL Server服务状态
1. 按 `Win + R`，输入 `services.msc`
2. 查找以下服务并确保它们正在运行：
   - `SQL Server (SQLEXPRESS)`
   - `SQL Server Browser`

### 启动SQL Server服务
如果服务未运行，右键点击服务选择"启动"

### 常见连接字符串
尝试以下不同的服务器名称：
- `.\SQLEXPRESS`
- `localhost\SQLEXPRESS`
- `(local)\SQLEXPRESS`
- `127.0.0.1\SQLEXPRESS`

## 📋 数据库内容概览

### 创建的表结构：
1. **Category** - 商品分类 (5条记录)
2. **Customer** - 客户信息 (2条记录)
3. **Product** - 产品信息 (10条记录)
4. **Supplier** - 供应商信息 (2条记录)
5. **Order** - 订单表 (空)
6. **OrderItem** - 订单项表 (空)
7. **CartItem** - 购物车表 (空)

### 测试账户：
- **管理员**: 用户名 `admin`, 密码 `123`, 邮箱 `<EMAIL>`
- **普通用户**: 用户名 `Jack`, 密码 `123`, 邮箱 `<EMAIL>`

### 商品分类：
- Fish (鱼类)
- Bugs (昆虫)
- Backyard (后院动物)
- Birds (鸟类)
- Endangered (濒危动物)

## ✅ 完成后的下一步

数据库创建成功后：

1. **在Visual Studio中运行项目**
   - 按 F5 启动调试
   - 浏览器会自动打开网站

2. **测试登录功能**
   - 使用 admin/123 测试管理员登录
   - 使用 Jack/123 测试普通用户登录

3. **测试注册功能**
   - 注册一个新用户账户
   - 验证注册和登录流程

4. **浏览商品功能**
   - 查看所有商品页面
   - 测试搜索功能

数据库创建完成后，整个项目就可以正常运行了！
