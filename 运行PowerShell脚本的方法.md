# 运行PowerShell脚本的方法

## 方法一：临时允许脚本执行（推荐）

1. **以管理员身份打开PowerShell**：
   - 按 `Win + X`，选择"Windows PowerShell (管理员)"
   - 或者搜索"PowerShell"，右键选择"以管理员身份运行"

2. **临时更改执行策略**：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **运行数据库创建脚本**：
   ```powershell
   cd "E:\Ai\Ai编程\兼职\宠物"
   .\CreateDatabase.ps1
   ```

4. **（可选）恢复原始策略**：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy Restricted -Scope CurrentUser
   ```

## 方法二：绕过执行策略运行单个脚本

```powershell
PowerShell -ExecutionPolicy Bypass -File ".\CreateDatabase.ps1"
```

## 方法三：手动创建数据库（如果PowerShell方法不可行）

### 使用SQL Server Management Studio (SSMS)

1. **打开SSMS**
2. **连接到SQL Server**：
   - 服务器名称：`.\SQLEXPRESS` 或 `localhost\SQLEXPRESS`
   - 身份验证：Windows身份验证
3. **执行SQL脚本**：
   - 文件 → 打开 → 文件
   - 选择 `Database_Scripts\Shop_sql2.sql`
   - 点击"执行"按钮

### 使用命令行 (sqlcmd)

```cmd
sqlcmd -S .\SQLEXPRESS -i "Database_Scripts\Shop_sql2.sql"
```

## 方法四：使用Visual Studio

1. **打开Visual Studio**
2. **视图** → **SQL Server对象资源管理器**
3. **连接到SQL Server实例**
4. **右键数据库** → **添加新数据库** → 命名为 `MyPetShop`
5. **右键新数据库** → **新建查询**
6. **复制并执行SQL脚本内容**

## 验证数据库创建成功

执行以下SQL查询验证：

```sql
USE MyPetShop;
SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';
SELECT COUNT(*) as CustomerCount FROM Customer;
SELECT COUNT(*) as ProductCount FROM Product;
```

应该返回：
- TableCount: 7 (7个表)
- CustomerCount: 2 (2个用户)
- ProductCount: 10 (10个产品)

## 如果遇到SQL Server连接问题

### 检查SQL Server服务状态
```cmd
# 检查服务状态
sc query "MSSQL$SQLEXPRESS"

# 启动服务（如果未运行）
net start "MSSQL$SQLEXPRESS"
```

### 常见连接字符串
- `.\SQLEXPRESS`
- `localhost\SQLEXPRESS`
- `(localdb)\MSSQLLocalDB` (如果使用LocalDB)

## 推荐执行顺序

1. 首先尝试**方法一**（临时更改执行策略）
2. 如果不行，使用**方法二**（绕过执行策略）
3. 如果PowerShell都不行，使用**方法三**（SSMS手动执行）
4. 最后选择**方法四**（Visual Studio）

选择最适合您环境的方法即可！
